/**
 * Centralized storage utilities with proper error handling
 * Provides a consistent interface for localStorage and sessionStorage operations
 */

// Storage keys constants
export const STORAGE_KEYS = {
  // Authentication
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  
  // Consent status is now managed in auth store - no localStorage needed
  
  // Temporary data
  TEMP_BOOKING_DATA: 'temp_booking_data',
  
  // User preferences
  THEME: 'theme',
  LANGUAGE: 'language',
};

/**
 * Safe localStorage operations with error handling
 */
export const localStorage = {
  /**
   * Get item from localStorage
   * @param {string} key - Storage key
   * @param {any} defaultValue - Default value if key doesn't exist or parsing fails
   * @returns {any} Parsed value or default value
   */
  getItem(key, defaultValue = null) {
    try {
      const item = window.localStorage.getItem(key);
      if (item === null) return defaultValue;
      return JSON.parse(item);
    } catch (error) {
      console.warn(`Failed to get localStorage item "${key}":`, error);
      return defaultValue;
    }
  },

  /**
   * Set item in localStorage
   * @param {string} key - Storage key
   * @param {any} value - Value to store (will be JSON stringified)
   * @returns {boolean} Success status
   */
  setItem(key, value) {
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Failed to set localStorage item "${key}":`, error);
      return false;
    }
  },

  /**
   * Remove item from localStorage
   * @param {string} key - Storage key
   * @returns {boolean} Success status
   */
  removeItem(key) {
    try {
      window.localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Failed to remove localStorage item "${key}":`, error);
      return false;
    }
  },

  /**
   * Clear all localStorage items
   * @returns {boolean} Success status
   */
  clear() {
    try {
      window.localStorage.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
      return false;
    }
  },

  /**
   * Check if localStorage is available
   * @returns {boolean} Availability status
   */
  isAvailable() {
    try {
      const testKey = '__localStorage_test__';
      window.localStorage.setItem(testKey, 'test');
      window.localStorage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }
};

/**
 * Safe sessionStorage operations with error handling
 */
export const sessionStorage = {
  /**
   * Get item from sessionStorage
   * @param {string} key - Storage key
   * @param {any} defaultValue - Default value if key doesn't exist or parsing fails
   * @returns {any} Parsed value or default value
   */
  getItem(key, defaultValue = null) {
    try {
      const item = window.sessionStorage.getItem(key);
      if (item === null) return defaultValue;
      return JSON.parse(item);
    } catch (error) {
      console.warn(`Failed to get sessionStorage item "${key}":`, error);
      return defaultValue;
    }
  },

  /**
   * Set item in sessionStorage
   * @param {string} key - Storage key
   * @param {any} value - Value to store (will be JSON stringified)
   * @returns {boolean} Success status
   */
  setItem(key, value) {
    try {
      window.sessionStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Failed to set sessionStorage item "${key}":`, error);
      return false;
    }
  },

  /**
   * Remove item from sessionStorage
   * @param {string} key - Storage key
   * @returns {boolean} Success status
   */
  removeItem(key) {
    try {
      window.sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Failed to remove sessionStorage item "${key}":`, error);
      return false;
    }
  },

  /**
   * Clear all sessionStorage items
   * @returns {boolean} Success status
   */
  clear() {
    try {
      window.sessionStorage.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear sessionStorage:', error);
      return false;
    }
  },

  /**
   * Check if sessionStorage is available
   * @returns {boolean} Availability status
   */
  isAvailable() {
    try {
      const testKey = '__sessionStorage_test__';
      window.sessionStorage.setItem(testKey, 'test');
      window.sessionStorage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }
};

/**
 * High-level storage operations for specific data types
 */
export const storage = {
  // Authentication
  auth: {
    getToken() {
      return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    },
    setToken(token) {
      return localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    },
    removeToken() {
      return localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    },
    getUser() {
      const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
      try {
        return userData ? JSON.parse(userData) : null;
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return null;
      }
    },
    setUser(userData) {
      try {
        return localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      } catch (error) {
        console.error('Error storing user data to localStorage:', error);
        return false;
      }
    },
    removeUser() {
      return localStorage.removeItem(STORAGE_KEYS.USER_DATA);
    },
    clearAuth() {
      const success1 = localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      const success2 = localStorage.removeItem(STORAGE_KEYS.USER_DATA);
      return success1 && success2;
    }
  },

  // Consent status is now managed in auth store - no localStorage needed

  // User preferences
  preferences: {
    getTheme() {
      return localStorage.getItem(STORAGE_KEYS.THEME, 'light');
    },
    setTheme(theme) {
      return localStorage.setItem(STORAGE_KEYS.THEME, theme);
    },
    getLanguage() {
      return localStorage.getItem(STORAGE_KEYS.LANGUAGE, 'en');
    },
    setLanguage(language) {
      return localStorage.setItem(STORAGE_KEYS.LANGUAGE, language);
    }
  },

  // Cleanup utilities
  cleanup: {
    /**
     * Clear all user-specific data (for logout)
     * @param {number} userId - User ID to clean up data for
     */
    clearUserData(userId) {
      console.log('🧹 Clearing user-specific data...');
      
      // Clear auth data
      storage.auth.clearAuth();
      
      // Consent status is now managed in auth store and cleared automatically
      
      // Clear any user-specific sessionStorage data
      sessionStorage.removeItem(STORAGE_KEYS.TEMP_BOOKING_DATA);
      
      console.log('✅ User data cleanup completed');
    },

    /**
     * Clear all application data (for complete reset)
     */
    clearAllData() {
      console.log('🧹 Clearing all application data...');
      localStorage.clear();
      sessionStorage.clear();
      console.log('✅ Complete data cleanup completed');
    },

    /**
     * Clear expired tokens and invalid data
     */
    clearExpiredData() {
      const token = storage.auth.getToken();
      
      if (token) {
        try {
          // Check if token is expired (basic check for JWT)
          const payload = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Date.now() / 1000;
          
          if (payload.exp && payload.exp < currentTime) {
            console.log('🧹 Clearing expired token');
            storage.auth.clearAuth();
          }
        } catch (error) {
          // If token is malformed, clear it
          console.log('🧹 Clearing malformed token');
          storage.auth.clearAuth();
        }
      }
    }
  }
};
