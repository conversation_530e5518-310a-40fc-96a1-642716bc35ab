/**
 * Authentication testing utilities
 * Use these functions to test and debug authentication issues
 */

import { authService } from '../features/auth/services/authApi';
import { storage } from './storage';

/**
 * Test authentication status and token validity
 */
export async function testAuthStatus() {
  console.log('🔍 Testing Authentication Status...');
  
  const token = storage.auth.getToken();
  const refreshToken = storage.auth.getRefreshToken();
  const user = storage.auth.getUser();
  
  console.log('📊 Current Auth State:', {
    hasToken: !!token,
    hasRefreshToken: !!refreshToken,
    hasUser: !!user,
    tokenLength: token?.length || 0,
    refreshTokenLength: refreshToken?.length || 0,
    user: user ? { id: user.id, email: user.email } : null
  });
  
  if (token) {
    try {
      // Check if token is expired
      const parts = token.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        const isExpired = payload.exp && payload.exp < currentTime;
        
        console.log('🔒 Token Details:', {
          exp: payload.exp ? new Date(payload.exp * 1000) : 'No expiration',
          now: new Date(),
          isExpired,
          timeLeft: payload.exp ? `${Math.floor((payload.exp - currentTime) / 60)} minutes` : 'N/A'
        });
        
        if (isExpired) {
          console.log('⚠️ Token is expired!');
          return { status: 'expired', token, refreshToken, user };
        }
      }
    } catch (error) {
      console.error('❌ Error parsing token:', error);
      return { status: 'invalid', token, refreshToken, user };
    }
  }
  
  return { status: token ? 'valid' : 'missing', token, refreshToken, user };
}

/**
 * Test login with credentials
 */
export async function testLogin(email, password) {
  console.log('🔐 Testing Login...');
  
  try {
    const result = await authService.login({ email, password });
    console.log('✅ Login successful:', result);
    
    // Check what was stored
    await testAuthStatus();
    
    return { success: true, result };
  } catch (error) {
    console.error('❌ Login failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test token refresh functionality
 */
export async function testTokenRefresh() {
  console.log('🔄 Testing Token Refresh...');
  
  const refreshToken = storage.auth.getRefreshToken();
  if (!refreshToken) {
    console.log('❌ No refresh token available');
    return { success: false, error: 'No refresh token' };
  }
  
  try {
    // Make a request that should trigger token refresh if needed
    const user = await authService.getCurrentUser();
    console.log('✅ Token refresh test successful:', user);
    
    // Check updated auth status
    await testAuthStatus();
    
    return { success: true, user };
  } catch (error) {
    console.error('❌ Token refresh test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test logout functionality
 */
export async function testLogout() {
  console.log('🚪 Testing Logout...');
  
  try {
    await authService.logout();
    console.log('✅ Logout successful');
    
    // Check that everything was cleared
    await testAuthStatus();
    
    return { success: true };
  } catch (error) {
    console.error('❌ Logout failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Comprehensive authentication test
 */
export async function runAuthTests(email = '<EMAIL>', password = 'test123') {
  console.log('🧪 Running Comprehensive Auth Tests...');
  
  const results = {};
  
  // Test 1: Initial auth status
  console.log('\n--- Test 1: Initial Auth Status ---');
  results.initialStatus = await testAuthStatus();
  
  // Test 2: Login
  console.log('\n--- Test 2: Login ---');
  results.login = await testLogin(email, password);
  
  if (results.login.success) {
    // Test 3: Token refresh
    console.log('\n--- Test 3: Token Refresh ---');
    results.tokenRefresh = await testTokenRefresh();
    
    // Test 4: Logout
    console.log('\n--- Test 4: Logout ---');
    results.logout = await testLogout();
  }
  
  console.log('\n🏁 Test Results Summary:', results);
  return results;
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  window.authTest = {
    testAuthStatus,
    testLogin,
    testTokenRefresh,
    testLogout,
    runAuthTests
  };
  
  console.log('🧪 Auth test functions available at window.authTest');
}
