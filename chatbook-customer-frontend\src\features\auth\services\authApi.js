import axios from 'axios';
import { storage } from '../../../utils/storage';

const API_BASE_URL = 'http://127.0.0.1:8000/api/v1';

// Create axios instance for auth API calls
const authApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// List of public endpoints that don't need authentication
const PUBLIC_ENDPOINTS = [
  '/auth/login/',
  '/auth/register/',
  '/auth/password-reset/',
  '/auth/social/google/',
  '/auth/social/facebook/',
  '/auth/social/apple/'
];

// Request interceptor to add auth token to requests (except public endpoints)
authApi.interceptors.request.use(
  (config) => {
    // Check if this is a public endpoint
    const isPublicEndpoint = PUBLIC_ENDPOINTS.some(endpoint =>
      config.url && config.url.includes(endpoint)
    );

    console.log('📤 API Request:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      isPublicEndpoint,
      hasAuthHeader: !!config.headers.Authorization,
      data: config.data,
      headers: config.headers
    });

    // Only add auth token for non-public endpoints
    if (!isPublicEndpoint) {
      const token = storage.auth.getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('🔑 Added auth token to request');
      }
    } else {
      console.log('🌐 Public endpoint - no auth token added');
    }

    return config;
  },
  (error) => {
    console.error('📤 Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors and token refresh
authApi.interceptors.response.use(
  (response) => {
    console.log('📥 API Response:', {
      status: response.status,
      statusText: response.statusText,
      url: response.config?.url,
      method: response.config?.method?.toUpperCase(),
      data: response.data
    });
    return response;
  },
  async (error) => {
    console.error('📥 API Error Response:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      data: error.response?.data,
      headers: error.response?.headers
    });

    const originalRequest = error.config;

    // Only handle 401 errors for protected endpoints (not login/register)
    if (error.response?.status === 401 && !originalRequest._retry) {
      const isPublicEndpoint = PUBLIC_ENDPOINTS.some(endpoint =>
        error.config?.url && error.config.url.includes(endpoint)
      );

      // Only attempt token refresh for protected endpoints
      if (!isPublicEndpoint) {
        console.log('🔒 401 on protected endpoint - attempting token refresh');
        originalRequest._retry = true;

        try {
          const refreshToken = storage.auth.getRefreshToken();
          if (refreshToken) {
            console.log('🔄 Attempting token refresh...');
            const response = await authApi.post('/auth/refresh/', {
              refresh: refreshToken,
            });

            const { access } = response.data;
            storage.auth.setToken(access);

            console.log('✅ Token refreshed successfully');

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${access}`;
            return authApi(originalRequest);
          } else {
            console.log('❌ No refresh token available');
            throw new Error('No refresh token');
          }
        } catch (refreshError) {
          console.log('❌ Token refresh failed:', refreshError.response?.data || refreshError.message);
          // Refresh failed, clear auth and redirect to login
          storage.auth.clearAuth();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        console.log('🌐 401 on public endpoint - not clearing tokens');
      }
    }
    return Promise.reject(error);
  }
);

// Token validation utilities
const tokenUtils = {
  // Check if token exists and is not expired
  isTokenValid() {
    const token = storage.auth.getToken();
    if (!token) {
      console.log('🔒 No token found');
      return false;
    }

    try {
      // JWT tokens have 3 parts separated by dots
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.log('🔒 Invalid token format');
        return false;
      }

      // Decode the payload (second part)
      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      if (payload.exp && payload.exp < currentTime) {
        console.log('🔒 Token expired:', {
          exp: new Date(payload.exp * 1000),
          now: new Date(),
          expired: true
        });
        return false;
      }

      console.log('🔒 Token valid:', {
        exp: payload.exp ? new Date(payload.exp * 1000) : 'No expiration',
        now: new Date(),
        timeLeft: payload.exp ? `${Math.floor((payload.exp - currentTime) / 60)} minutes` : 'N/A'
      });
      return true;
    } catch (error) {
      console.error('🔒 Token validation error:', error);
      return false;
    }
  },

  // Clear invalid tokens
  clearInvalidToken() {
    if (!this.isTokenValid()) {
      console.log('🧹 Clearing invalid token');
      storage.auth.clearAuth();
      return true;
    }
    return false;
  }
};

// Auth API functions
export const authService = {
  // Login with email/phone and password
  async login(credentials) {
    try {
      console.log('🔐 Login attempt with credentials:', {
        email: credentials.email,
        username: credentials.username, // Keep both for debugging
        password: credentials.password ? '[REDACTED]' : 'undefined',
        hasPassword: !!credentials.password
      });

      const response = await authApi.post('/auth/login/', credentials);
      console.log('✅ Login successful:', {
        status: response.status,
        hasToken: !!response.data?.token,
        hasUser: !!response.data?.user
      });

      const { access, refresh, user } = response.data;

      // Store tokens and user data
      storage.auth.setToken(access);
      if (refresh) {
        storage.auth.setRefreshToken(refresh);
      }
      storage.auth.setUser(user);

      // Check user consent status and store in auth store (single source of truth)
      try {
        console.log('🔍 Checking consent status from API: /business-customers/me/forms/');
        const consentResponse = await authApi.get('/business-customers/me/forms/');
        const consentStatus = consentResponse.data;

        console.log('📝 API consent response:', consentStatus);

        // Return consent status to be handled by the calling function
        return { user, access, refresh, consentStatus };

      } catch (consentError) {
        console.warn('⚠️ Failed to check consent status on login:', consentError);
        console.log('API Error details:', {
          status: consentError.response?.status,
          statusText: consentError.response?.statusText,
          data: consentError.response?.data
        });

        // Return with default consent status
        return {
          user,
          access,
          refresh,
          consentStatus: {
            all_forms_completed: false,
            missing_forms: []
          }
        };
      }

      // This return is now handled above with consent status
    } catch (error) {
      console.error('❌ Login error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          data: error.config?.data,
          headers: error.config?.headers
        }
      });

      // Try to extract meaningful error message
      let errorMessage = 'Login failed';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        } else {
          errorMessage = JSON.stringify(error.response.data);
        }
      }

      throw new Error(errorMessage);
    }
  },

  // Logout
  async logout() {
    try {
      // Try to clear booking sessions before logout
      try {
        await authApi.delete('/booking-sessions/clear/', {
          params: { business_id: 1 } // Default business ID
        });
        console.log('🧹 Cleared booking sessions on logout');
      } catch (bookingError) {
        console.warn('⚠️ Failed to clear booking sessions on logout:', bookingError);
      }

      // Send refresh token for proper blacklisting
      const refreshToken = storage.auth.getRefreshToken();
      await authApi.post('/auth/logout/', refreshToken ? { refresh_token: refreshToken } : {});
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Get user ID before clearing user data
      const userData = storage.auth.getUser();
      const userId = userData?.id;

      // Clear user-specific data using centralized storage
      if (userId) {
        storage.cleanup.clearUserData(userId);
      } else {
        storage.auth.clearAuth();
      }

      console.log('🚪 Cleared all storage on logout');
    }
  },

  // Register new user
  async register(userData) {
    try {
      const response = await authApi.post('/auth/register/', userData);
      const { access, refresh, user } = response.data;

      // Store tokens and user data
      storage.auth.setToken(access);
      if (refresh) {
        storage.auth.setRefreshToken(refresh);
      }
      storage.auth.setUser(user);

      return { access, refresh, user };
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  },

  // Get current user profile
  async getCurrentUser() {
    try {
      const response = await authApi.get('/auth/user/');
      return response.data;
    } catch (error) {
      console.error('Get user error:', error);
      throw new Error(error.response?.data?.message || error.response?.data?.detail || 'Failed to get user data');
    }
  },

  // Password reset request
  async requestPasswordReset(email) {
    try {
      const response = await authApi.post('/auth/password-reset/', { email });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Password reset request failed');
    }
  },

  // Social login (Google, Facebook, Apple)
  async socialLogin(provider, token) {
    try {
      const response = await authApi.post(`/auth/social/${provider}/`, { token });
      const { access, refresh, user } = response.data;

      // Store tokens and user data
      storage.auth.setToken(access);
      if (refresh) {
        storage.auth.setRefreshToken(refresh);
      }
      storage.auth.setUser(user);

      return { access, refresh, user };
    } catch (error) {
      throw new Error(error.response?.data?.message || `${provider} login failed`);
    }
  },

  // Check if user is authenticated
  isAuthenticated() {
    const token = storage.auth.getToken();
    return !!token;
  },

  // Get current consent status from auth store (single source of truth)
  getConsentStatus() {
    // Import will be handled by the calling component
    return null; // This will be implemented in the auth store hook
  },

  // Check if user has completed all required forms
  hasCompletedAllForms() {
    // Import will be handled by the calling component
    return false; // This will be implemented in the auth store hook
  },

  // Refresh consent status from API
  async refreshConsentStatus() {
    try {
      console.log('🔄 Refreshing consent status from API: /business-customers/me/forms/');
      const response = await authApi.get('/business-customers/me/forms/');
      console.log('📝 Refreshed consent response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to refresh consent status:', error);
      console.log('API Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      throw error;
    }
  },

  // Get stored user data
  getStoredUser() {
    return storage.auth.getUser();
  },

  // Get stored token
  getStoredToken() {
    return storage.auth.getToken();
  },

  // Get stored refresh token
  getStoredRefreshToken() {
    return storage.auth.getRefreshToken();
  }
};

export default authService;
