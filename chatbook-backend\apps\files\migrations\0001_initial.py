# Generated by Django 4.2.22 on 2025-08-07 09:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_id', models.CharField(db_index=True, max_length=50, unique=True)),
                ('file_name', models.CharField(max_length=255)),
                ('file_size', models.BigIntegerField()),
                ('file_type', models.CharField(choices=[('customer_import', 'Customer Import'), ('appointment_import', 'Appointment Import'), ('service_import', 'Service Import'), ('employee_import', 'Employee Import'), ('document', 'Document'), ('image', 'Image'), ('other', 'Other')], default='other', max_length=50)),
                ('content_type', models.CharField(blank=True, max_length=100)),
                ('file_hash', models.CharField(blank=True, max_length=32)),
                ('s3_key', models.CharField(max_length=500)),
                ('s3_bucket', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('processing', 'Processing'), ('processed', 'Processed'), ('failed', 'Failed'), ('archived', 'Archived')], default='uploaded', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('skip_duplicates', models.BooleanField(default=True)),
                ('update_existing', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_files', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'files_uploaded_file',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ImportResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_rows', models.IntegerField(default=0)),
                ('successful_imports', models.IntegerField(default=0)),
                ('failed_imports', models.IntegerField(default=0)),
                ('skipped_rows', models.IntegerField(default=0)),
                ('import_type', models.CharField(max_length=50)),
                ('errors', models.JSONField(blank=True, default=list)),
                ('warnings', models.JSONField(blank=True, default=list)),
                ('processing_started_at', models.DateTimeField(blank=True, null=True)),
                ('processing_completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uploaded_file', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='import_result', to='files.uploadedfile')),
            ],
            options={
                'db_table': 'files_import_result',
            },
        ),
        migrations.CreateModel(
            name='FileProcessingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(choices=[('info', 'Info'), ('warning', 'Warning'), ('error', 'Error'), ('debug', 'Debug')], default='info', max_length=10)),
                ('message', models.TextField()),
                ('step', models.CharField(blank=True, max_length=100)),
                ('details', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('uploaded_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='processing_logs', to='files.uploadedfile')),
            ],
            options={
                'db_table': 'files_processing_log',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='uploadedfile',
            index=models.Index(fields=['file_id'], name='files_uploa_file_id_194593_idx'),
        ),
        migrations.AddIndex(
            model_name='uploadedfile',
            index=models.Index(fields=['status'], name='files_uploa_status_6b4f54_idx'),
        ),
        migrations.AddIndex(
            model_name='uploadedfile',
            index=models.Index(fields=['uploaded_by', 'created_at'], name='files_uploa_uploade_323f78_idx'),
        ),
        migrations.AddIndex(
            model_name='uploadedfile',
            index=models.Index(fields=['file_type', 'status'], name='files_uploa_file_ty_07a6ce_idx'),
        ),
        migrations.AddIndex(
            model_name='importresult',
            index=models.Index(fields=['import_type'], name='files_impor_import__9f0f73_idx'),
        ),
        migrations.AddIndex(
            model_name='importresult',
            index=models.Index(fields=['created_at'], name='files_impor_created_136a25_idx'),
        ),
        migrations.AddIndex(
            model_name='fileprocessinglog',
            index=models.Index(fields=['uploaded_file', 'created_at'], name='files_proce_uploade_5c5b8a_idx'),
        ),
        migrations.AddIndex(
            model_name='fileprocessinglog',
            index=models.Index(fields=['level'], name='files_proce_level_57af86_idx'),
        ),
    ]
