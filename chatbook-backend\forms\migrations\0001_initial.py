# Generated by Django 4.2.22 on 2025-08-07 09:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customers', '0001_initial'),
        ('employees', '0001_initial'),
        ('business', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FormSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.JSONField(help_text='JSON content of the form submission')),
                ('status', models.CharField(choices=[('Submitted', 'Submitted'), ('Reviewed', 'Reviewed'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Submitted', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_customer', models.ForeignKey(help_text='Business–Customer relationship this submission belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='form_submissions', to='business.businesscustomer')),
            ],
            options={
                'verbose_name': 'Form Submission',
                'verbose_name_plural': 'Form Submissions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Signature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('signer_type', models.CharField(choices=[('customer', 'Customer'), ('employee', 'Employee')], max_length=20)),
                ('signature_data', models.TextField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='signatures', to='business.business')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='signatures', to='customers.customerprofile')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='signatures', to='employees.employee')),
                ('form_submission', models.ForeignKey(blank=True, help_text='Form submission this signature is associated with. Required for customer signatures.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='signatures', to='forms.formsubmission')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='signatures', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Signature',
                'verbose_name_plural': 'Signatures',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FormTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('document_type', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('Published', 'Published'), ('Draft', 'Draft')], default='Draft', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content', models.JSONField(default=dict, help_text='JSON content of the form template')),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='form_templates', to='business.business')),
            ],
            options={
                'verbose_name': 'Form Template',
                'verbose_name_plural': 'Form Templates',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='form_template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='forms.formtemplate'),
        ),
        migrations.AddField(
            model_name='formsubmission',
            name='submitted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='form_submissions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='BusinessRequiredForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_required', models.BooleanField(default=True, help_text='Whether this form is required for customers')),
                ('required_for_new_customers', models.BooleanField(default=True, help_text='Whether this form is required for new customers')),
                ('required_for_existing_customers', models.BooleanField(default=False, help_text='Whether this form is required for existing customers')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order in which forms should be presented to customers')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='required_forms', to='business.business')),
                ('form_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_requirements', to='forms.formtemplate')),
            ],
            options={
                'verbose_name': 'Business Required Form',
                'verbose_name_plural': 'Business Required Forms',
                'ordering': ['business', 'order', 'form_template__name'],
                'unique_together': {('business', 'form_template')},
            },
        ),
    ]
