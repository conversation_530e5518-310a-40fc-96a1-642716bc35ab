# Generated by Django 4.2.22 on 2025-08-07 09:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('services', '0001_initial'),
        ('employees', '0001_initial'),
        ('business', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WaitlistEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('customer_name', models.CharField(max_length=255)),
                ('phone_number', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('notes', models.TextField(blank=True, help_text='Special requests or notes for this waitlist entry')),
                ('status', models.CharField(choices=[('current', 'Current'), ('expired', 'Expired')], default='current', max_length=10)),
                ('priority_rule', models.CharField(choices=[('manual', 'Manual')], default='manual', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('expired_at', models.DateTimeField(blank=True, null=True)),
                ('add_ons', models.ManyToManyField(blank=True, related_name='waitlist_entries', to='services.addon')),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='waitlist_entries', to='business.business')),
                ('employees', models.ManyToManyField(blank=True, help_text='Preferred employees for this waitlist entry', related_name='waitlist_entries', to='employees.employee')),
                ('services', models.ManyToManyField(blank=True, related_name='waitlist_entries', to='services.service')),
            ],
            options={
                'verbose_name': 'Waitlist Entry',
                'verbose_name_plural': 'Waitlist Entries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PreferredWindow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_datetime', models.DateTimeField()),
                ('end_datetime', models.DateTimeField()),
                ('entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='windows', to='waitlist.waitlistentry')),
            ],
            options={
                'indexes': [models.Index(fields=['start_datetime', 'end_datetime'], name='waitlist_pr_start_d_3ada7a_idx')],
            },
        ),
    ]
